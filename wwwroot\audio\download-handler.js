/**
 * 角色卡片下载处理程序
 * 实现从本地Downloads文件夹获取文件并下载
 */

class DownloadHandler {
  constructor() {
    this.downloadBasePath = 'Downloads/'; // 下载文件夹路径
    this.downloadCounts = {};
    this.initDownloadCounts();
  }

  // 初始化下载计数
  initDownloadCounts() {
    // 尝试从localStorage获取下载计数
    const savedCounts = localStorage.getItem('downloadCounts');
    if (savedCounts) {
      try {
        this.downloadCounts = JSON.parse(savedCounts);
        // 确保新角色也被初始化
        this.ensureAllCharactersInitialized();
      } catch (e) {
        console.error('解析下载计数数据失败:', e);
        this.resetDownloadCounts();
      }
    } else {
      this.resetDownloadCounts();
    }
  }

  // 确保所有角色都被初始化
  ensureAllCharactersInitialized() {
    const allCharacters = [
      '今汐',
      '长离',
      '守岸人',
      '椿',
      '珂莱塔',
      '洛可可',
      '菲比',
      '坎特蕾拉',
      '夏空',
      '卡提希娅',
      '露帕',
      '弗洛洛',
    ];
    let needsSave = false;

    allCharacters.forEach(character => {
      if (this.downloadCounts[character] === undefined) {
        this.downloadCounts[character] = 0;
        needsSave = true;
      }
    });

    if (needsSave) {
      this.saveDownloadCounts();
    }
  }

  // 重置下载计数
  resetDownloadCounts() {
    // 设置默认的下载计数为0
    const characters = [
      '今汐',
      '长离',
      '守岸人',
      '椿',
      '珂莱塔',
      '洛可可',
      '菲比',
      '坎特蕾拉',
      '夏空',
      '卡提希娅',
      '露帕',
      '弗洛洛',
    ];
    this.downloadCounts = {};

    characters.forEach(character => {
      this.downloadCounts[character] = 0;
    });

    // 保存到localStorage
    this.saveDownloadCounts();
  }

  // 保存下载计数到localStorage
  saveDownloadCounts() {
    localStorage.setItem('downloadCounts', JSON.stringify(this.downloadCounts));
  }

  // 获取指定角色的下载路径
  getDownloadPath(character) {
    // 角色名到文件名的映射
    const fileNameMap = new Map([
      ['今汐', '今汐.png'],
      ['长离', '长离.png'],
      ['守岸人', '守岸人.png'],
      ['椿', '椿.png'],
      ['珂莱塔', '珂莱塔.png'],
      ['洛可可', '洛可可.png'],
      ['菲比', '菲比.png'],
      ['坎特蕾拉', '坎特蕾拉.png'],
      ['夏空', '夏空.png'],
      ['卡提希娅', '卡提希娅.png'],
      ['露帕', '露帕.png'],
      ['弗洛洛', '弗洛洛.png'],
    ]);

    if (!fileNameMap.has(character)) {
      console.error(`未找到角色 ${character} 的文件名映射`);
      return null;
    }

    return this.downloadBasePath + fileNameMap.get(character);
  }

  // 处理下载请求
  async handleDownload(character) {
    console.log(`开始处理下载请求: ${character}`);
    const downloadPath = this.getDownloadPath(character);
    console.log(`获取到下载路径: ${downloadPath}`);
    if (!downloadPath) {
      console.error(`无法获取角色 ${character} 的下载路径`);
      return false;
    }

    try {
      // 先检查文件是否存在
      const response = await fetch(downloadPath, { method: 'HEAD' });
      if (!response.ok) {
        console.error(`文件不存在或无法访问: ${downloadPath}, 状态: ${response.status}`);
        alert(`下载失败，请确保 Downloads 文件夹中有 ${character} 的图片文件`);
        return false;
      }

      // 增加下载计数
      if (!this.downloadCounts[character]) {
        this.downloadCounts[character] = 0;
      }
      this.downloadCounts[character]++;
      this.saveDownloadCounts();

      // 触发下载
      const link = document.createElement('a');
      link.href = downloadPath;
      link.download = character + '.png';
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`成功下载角色 ${character}，当前下载次数: ${this.downloadCounts[character]}`);
      return true;
    } catch (e) {
      console.error(`下载角色 ${character} 失败:`, e);
      alert(`下载失败，请确保 Downloads 文件夹中有 ${character} 的图片文件`);
      return false;
    }
  }

  // 获取指定角色的下载次数
  getDownloadCount(character) {
    return this.downloadCounts[character] || 0;
  }

  // 更新UI上的下载计数
  updateDownloadCountsUI() {
    document.querySelectorAll('.count').forEach(countElement => {
      const character = countElement.getAttribute('data-character');
      if (character && this.downloadCounts[character] !== undefined) {
        countElement.textContent = this.downloadCounts[character];
      }
    });
  }
}

// 全局下载处理器实例
window.downloadHandler = new DownloadHandler();
