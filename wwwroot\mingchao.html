<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Wuthering Waves - 游星的档案馆</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500&family=Cormorant+Garamond:wght@300;400&family=Noto+Serif+SC:wght@300;400;500;600&family=Ma+Shan+Zheng&family=Zhi+Mang+Xing&family=ZCOOL+XiaoWei&family=ZCOOL+QingKe+HuangYou&display=swap"
      rel="stylesheet"
    />

    <!-- 预加载图片资源 -->
    <link rel="preload" href="images/characters/jinxi.png" as="image" />
    <link rel="preload" href="images/characters/changli.png" as="image" />
    <link rel="preload" href="images/characters/shouanren.png" as="image" />
    <link rel="preload" href="images/characters/chun.png" as="image" />
    <link rel="preload" href="images/characters/kelaita.png" as="image" />
    <link rel="preload" href="images/characters/luokeke.png" as="image" />
    <link rel="preload" href="images/characters/feibi.png" as="image" />
    <link rel="preload" href="images/characters/kantelaila.png" as="image" />
    <link rel="preload" href="images/characters/xiakong.png" as="image" />
    <link rel="preload" href="images/characters/katixi.png" as="image" />
    <link rel="preload" href="images/characters/lupa.png" as="image" />
    <link rel="preload" href="images/characters/fuluoluo.png" as="image" />
    <link rel="preload" href="images/bg.jpg" as="image" />

    <!-- 预加载音频资源 -->
    <link rel="preload" href="audio/background2-「酣梦于彼岸深红」.mp3" as="audio" type="audio/mpeg" />
    <link rel="preload" href="audio/audio-controller.js" as="script" />
    <link rel="preload" href="audio/page-loader.js" as="script" />
    <link rel="preload" href="audio/download-handler.js" as="script" />

    <!-- 优先加载脚本文件 -->
    <script src="audio/download-handler.js"></script>
    <script src="audio/page-loader.js"></script>
    <script src="audio/audio-controller.js"></script>

    <!-- 添加Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500&family=Cormorant+Garamond:wght@300;400&family=Playfair+Display:ital@0;1&display=swap');

      :root {
        --primary-color: #48cae4;
        --primary-dark: #0096c7;
        --primary-light: #90e0ef;
        --bg-dark: #0a192f;
        --bg-medium: #112240;
        --bg-light: #1a365d;
        --text-light: #fff;
        --text-muted: rgba(255, 255, 255, 0.7);
        --border-color: rgba(255, 255, 255, 0.1);
        --card-bg: rgba(13, 25, 42, 0.8);
        --card-border: rgba(72, 202, 228, 0.3);
        --card-shadow: rgba(72, 202, 228, 0.2);
        --chinese-font: 'Noto Serif SC', 'ZCOOL XiaoWei', 'Microsoft YaHei', 'PingFang SC', serif;
        --chinese-title-font: 'Ma Shan Zheng', 'Zhi Mang Xing', 'ZCOOL QingKe HuangYou', cursive;
        --animation-duration: 1.2s;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: var(--chinese-font);
        background: linear-gradient(135deg, var(--bg-dark), var(--bg-medium), var(--bg-light));
        color: var(--text-light);
        margin: 0;
        padding: 0;
        min-height: 100vh;
        position: relative;
        opacity: 0;
        animation: fadeIn 1.5s ease forwards;
      }

      @keyframes fadeIn {
        0% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }

      @keyframes slideInFromTop {
        0% {
          transform: translateY(-50px);
          opacity: 0;
        }
        100% {
          transform: translateY(0);
          opacity: 1;
        }
      }

      @keyframes slideInFromLeft {
        0% {
          transform: translateX(-50px);
          opacity: 0;
        }
        100% {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes slideInFromBottom {
        0% {
          transform: translateY(30px);
          opacity: 0;
        }
        100% {
          transform: translateY(0);
          opacity: 1;
        }
      }

      @keyframes scaleIn {
        0% {
          transform: scale(0.9);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }

      @keyframes glowPulse {
        0% {
          box-shadow: 0 0 5px rgba(72, 202, 228, 0.3);
        }
        50% {
          box-shadow: 0 0 20px rgba(72, 202, 228, 0.6);
        }
        100% {
          box-shadow: 0 0 5px rgba(72, 202, 228, 0.3);
        }
      }

      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('images/bg.jpg');
        background-size: cover;
        background-position: center;
        opacity: 0;
        z-index: -1;
        animation: fadeIn 2s ease forwards;
        animation-delay: 0.3s;
      }

      .header {
        text-align: center;
        padding: 2rem 0;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        position: relative;
        opacity: 0;
        animation: slideInFromTop var(--animation-duration) ease forwards;
        animation-delay: 0.3s;
      }

      .header h1 {
        font-size: 3rem;
        margin: 0;
        background: linear-gradient(to right, var(--primary-color), var(--primary-light), #ade8f4);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        font-weight: 300;
        letter-spacing: 2px;
        font-family: 'Cinzel', serif;
        opacity: 0;
        animation: scaleIn 1.5s ease forwards;
        animation-delay: 0.8s;
      }

      .header-subtitle {
        font-family: var(--chinese-font);
        font-style: italic;
        color: var(--text-muted);
        margin-top: 0.5rem;
        font-size: 1.2rem;
        letter-spacing: 1px;
        opacity: 0;
        animation: fadeIn 1.5s ease forwards;
        animation-delay: 1.2s;
      }

      .back-btn {
        position: absolute;
        top: 20px;
        left: 20px;
        padding: 0.8rem 1.5rem;
        background: rgba(72, 202, 228, 0.2);
        border: 1px solid var(--primary-color);
        color: #fff;
        border-radius: 30px;
        text-decoration: none;
        font-size: 1rem;
        transition: all 0.3s ease;
        z-index: 10;
        box-shadow: 0 0 15px rgba(72, 202, 228, 0.3);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Cinzel', var(--chinese-font);
        letter-spacing: 1px;
        text-transform: capitalize;
        opacity: 0;
        animation: slideInFromLeft var(--animation-duration) ease forwards;
        animation-delay: 0.5s;
      }

      .back-btn:hover {
        background: rgba(72, 202, 228, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(72, 202, 228, 0.5);
        animation: glowPulse 2s infinite;
      }

      .back-btn::before {
        content: '←';
        font-size: 1.2rem;
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
        opacity: 0;
        animation: fadeIn 1.5s ease forwards;
        animation-delay: 0.8s;
      }

      .characters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
        gap: 3rem;
        margin-bottom: 3rem;
      }

      .character-card {
        background: var(--card-bg);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 20px var(--card-shadow);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        cursor: pointer;
        position: relative;
        backdrop-filter: blur(8px);
        border: 1px solid var(--card-border);
        text-decoration: none;
        color: var(--text-light);
        max-width: 450px;
        margin: 0 auto;
        height: 600px;
        opacity: 0;
        transform: translateY(30px);
      }

      .character-card:hover {
        transform: translateY(-15px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6), 0 0 30px var(--card-shadow);
        border-color: var(--primary-color);
      }

      .character-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
        opacity: 0;
        transition: opacity 0.4s ease;
      }

      .character-card:hover::before {
        opacity: 1;
      }

      .character-image-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        height: 100%;
      }

      .character-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        transition: transform 0.5s ease;
      }

      /* 为特定角色添加图片位置调整 */
      .character-card[data-character='长离'] .character-image {
        object-position: center;
      }

      .character-card[data-character='守岸人'] .character-image {
        object-position: center;
      }

      .character-card[data-character='卡提希娅'] .character-image {
        object-position: center;
        object-fit: cover;
      }

      .character-card:hover .character-image {
        transform: scale(1.03);
      }

      .character-info-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to top,
          rgba(13, 25, 42, 0.95) 0%,
          rgba(13, 25, 42, 0.8) 40%,
          rgba(13, 25, 42, 0.4) 70%,
          rgba(13, 25, 42, 0) 100%
        );
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        padding: 2rem;
        opacity: 0;
        transition: opacity 0.4s ease;
      }

      .character-card:hover .character-info-overlay {
        opacity: 1;
      }

      .character-name {
        margin: 0 0 0.5rem 0;
        font-size: 2.2rem;
        color: var(--primary-color);
        font-weight: 500;
        letter-spacing: 2px;
        position: relative;
        display: inline-block;
        text-shadow: 0 0 10px rgba(72, 202, 228, 0.5);
        font-family: var(--chinese-title-font);
      }

      .character-name::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 60px;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), transparent);
      }

      .character-title {
        margin: 1rem 0;
        font-size: 1.1rem;
        color: var(--text-muted);
        font-style: italic;
        font-family: var(--chinese-font);
        letter-spacing: 1px;
      }

      .character-description {
        margin-top: 1.5rem;
        font-size: 1.1rem;
        line-height: 1.8;
        color: var(--text-light);
        text-align: left;
        font-family: var(--chinese-font);
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }

      .character-rarity {
        margin-top: 1.5rem;
        display: flex;
        gap: 4px;
      }

      .star {
        color: #ffd700;
        font-size: 1rem;
        text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
      }

      .character-label {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 1.2rem;
        background: linear-gradient(
          to top,
          rgba(13, 25, 42, 0.95) 0%,
          rgba(13, 25, 42, 0.7) 80%,
          rgba(13, 25, 42, 0) 100%
        );
        transition: opacity 0.3s ease;
      }

      .character-card:hover .character-label {
        opacity: 0;
      }

      /* 下载按钮和下载量样式 */
      .download-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        background: rgba(72, 202, 228, 0.3);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        text-decoration: none;
        font-size: 1.2rem;
        z-index: 10;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        backdrop-filter: blur(5px);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .character-card:hover .download-btn {
        opacity: 1;
        transform: translateY(0);
      }

      .download-btn:hover {
        background: rgba(72, 202, 228, 0.6);
        transform: scale(1.1);
      }

      .download-count {
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 20px;
        padding: 3px 10px;
        font-size: 0.8rem;
        color: #fff;
        display: flex;
        align-items: center;
        gap: 5px;
        z-index: 10;
        backdrop-filter: blur(5px);
      }

      .download-count i {
        font-size: 0.9rem;
        color: rgba(72, 202, 228, 0.8);
      }

      @media (max-width: 768px) {
        .characters-grid {
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 2rem;
        }

        .character-card {
          height: 500px;
        }

        .character-info-overlay {
          padding: 1.5rem;
        }

        .character-name {
          font-size: 1.8rem;
        }

        .character-description {
          font-size: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <a href="index.html" class="back-btn">Home</a>

    <header class="header">
      <h1>Wuthering Waves</h1>
      <p class="header-subtitle">Echo of the Elements</p>
    </header>

    <div class="container">
      <div class="characters-grid">
        <!-- 角色卡1 - 今汐 -->
        <div class="character-card" data-character="今汐">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/jinxi.png" alt="今汐" />
            <a href="javascript:void(0)" class="download-btn" title="下载今汐">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="今汐">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">今汐</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">今汐</h3>
              <p class="character-title">寒尽觉春生</p>
              <div class="character-description">
                <p>"春回岁往谁与共，桃夭灼灼牵丝动。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡2 - 长离 -->
        <div class="character-card" data-character="长离">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/changli.png" alt="长离" />
            <a href="javascript:void(0)" class="download-btn" title="下载长离">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="长离">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">长离</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">长离</h3>
              <p class="character-title">离火弈长生</p>
              <div class="character-description">
                <p>"——你是被雨困在了这里吗？"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡3 - 守岸人 -->
        <div class="character-card" data-character="守岸人">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/shouanren.png" alt="守岸人" />
            <a href="javascript:void(0)" class="download-btn" title="下载守岸人">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="守岸人">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">守岸人</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">守岸人</h3>
              <p class="character-title">为你而生</p>
              <div class="character-description">
                <p>"守岸人，这个称呼就很好。它表示，某种因你而有的意义和决心。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡4 - 椿 -->
        <div class="character-card" data-character="椿">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/chun.png" alt="椿" />
            <a href="javascript:void(0)" class="download-btn" title="下载椿">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="椿">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">椿</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">椿</h3>
              <p class="character-title">边缘独舞</p>
              <div class="character-description">
                <p>"无论是怎样的未来……我和你的相遇，果然是命中注定呢。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡5 - 珂莱塔 -->
        <div class="character-card" data-character="珂莱塔">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/kelaita.png" alt="珂莱塔" />
            <a href="javascript:void(0)" class="download-btn" title="下载珂莱塔">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="珂莱塔">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">珂莱塔</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">珂莱塔</h3>
              <p class="character-title">普莱修斯的天作之合</p>
              <div class="character-description">
                <p>"一场灵感的诠释，一段被构建的事实，一个由多种材质拼贴而成的艺术品。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡6 - 洛可可 -->
        <div class="character-card" data-character="洛可可">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/luokeke.png" alt="洛可可" />
            <a href="javascript:void(0)" class="download-btn" title="下载洛可可">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="洛可可">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">洛可可</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">洛可可</h3>
              <p class="character-title">E lucevan le stelle</p>
              <div class="character-description">
                <p>"E non ho amato mai tanto la vita。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡7 - 菲比 -->
        <div class="character-card" data-character="菲比">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/feibi.png" alt="菲比" />
            <a href="javascript:void(0)" class="download-btn" title="下载菲比">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="菲比">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">菲比</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">菲比</h3>
              <p class="character-title">微笑的国度与闪耀的每日</p>
              <div class="character-description">
                <p>"我赞颂，这因你而诞生的，闪耀的每一天。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡8 - 坎特蕾拉 -->
        <div class="character-card" data-character="坎特蕾拉">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/kantelaila.png" alt="坎特蕾拉" />
            <a href="javascript:void(0)" class="download-btn" title="下载坎特蕾拉">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="坎特蕾拉">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">坎特蕾拉</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">坎特蕾拉</h3>
              <p class="character-title">无星之海</p>
              <div class="character-description">
                <p>"……那么，海的尽头又是什么呢？"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡9 - 夏空 -->
        <div class="character-card" data-character="夏空">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/xiakong.png" alt="夏空" />
            <div class="character-label">
              <h3 class="character-name">夏空</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">夏空</h3>
              <p class="character-title">永昼叙事诗</p>
              <div class="character-description">
                <p>"关于为你而写的那首歌……我一定会再找到你，也一定会再唱给你听的！"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡10 - 卡提希娅 -->
        <div class="character-card" data-character="卡提希娅">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/katixi.png" alt="卡提希娅" />
            <a href="javascript:void(0)" class="download-btn" title="下载卡提希娅">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="卡提希娅">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">卡提希娅</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">卡提希娅</h3>
              <p class="character-title">献剑于神的少女们</p>
              <div class="character-description">
                <p>"即使身处命运的漩涡，我也有想要坚持的事。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡11 - 露帕 -->
        <div class="character-card" data-character="露帕">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/lupa.png" alt="露帕" />
            <a href="javascript:void(0)" class="download-btn" title="下载露帕">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="露帕">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">露帕</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">露帕</h3>
              <p class="character-title">致炽焰予战歌</p>
              <div class="character-description">
                <p>"以长照不灭的太阳起誓，我的搭档。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色卡12 - 弗洛洛 -->
        <div class="character-card" data-character="弗洛洛">
          <div class="character-image-container">
            <img class="character-image" src="images/characters/fuluoluo.png" alt="弗洛洛" />
            <a href="javascript:void(0)" class="download-btn" title="下载弗洛洛">
              <i class="fas fa-download"></i>
            </a>
            <div class="download-count">
              <i class="fas fa-cloud-download-alt"></i>
              <span class="count" data-character="弗洛洛">0</span>
            </div>
            <div class="character-label">
              <h3 class="character-name">弗洛洛</h3>
            </div>
            <div class="character-info-overlay">
              <h3 class="character-name">弗洛洛</h3>
              <p class="character-title">女巫安息日与夜会之梦</p>
              <div class="character-description">
                <p>"我或曾梦见，与你亲密无间；醒来后发现，你我形同陌路。"</p>
              </div>
              <div class="character-rarity">
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
                <span class="star">★</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // 确保音频控制器正常工作
        if (window.audioController && window.audioController.audio) {
          // 调整音频控制器的z-index，确保它显示在最上层
          const audioControl = document.getElementById('audioControl');
          if (audioControl) {
            audioControl.style.zIndex = '10000';
          }

          // 强制在页面加载时恢复音频播放状态
          const audioState = localStorage.getItem('audioState') || 'on';
          if (audioState === 'on' && window.audioController.audio.paused) {
            window.audioController.audio.play().catch(e => {
              console.log('自动播放被阻止，等待用户交互', e);
            });
          }
        }

        // 为角色卡片添加动画
        const cards = document.querySelectorAll('.character-card');
        cards.forEach((card, index) => {
          card.style.animation = `slideInFromBottom 0.8s ease forwards`;
          card.style.animationDelay = `${0.3 + index * 0.15}s`;
        });

        // 更新UI上的下载计数
        if (window.downloadHandler) {
          window.downloadHandler.updateDownloadCountsUI();
        }

        // 下载计数功能
        document.querySelectorAll('.download-btn').forEach(btn => {
          btn.addEventListener('click', function (e) {
            e.preventDefault();

            // 获取对应的角色名
            const characterCard = this.closest('.character-card');
            const characterName = characterCard.getAttribute('data-character');

            // 使用下载处理器处理下载
            if (window.downloadHandler) {
              const success = window.downloadHandler.handleDownload(characterName);
              if (success) {
                // 更新UI上的下载计数
                const countElement = characterCard.querySelector('.count');
                if (countElement) {
                  countElement.textContent = window.downloadHandler.getDownloadCount(characterName);
                }
              } else {
                console.error(`下载角色 ${characterName} 失败`);
                alert(`下载失败，请确保 DownLoad 文件夹中有 ${characterName} 的图片文件`);
              }
            } else {
              console.error('下载处理器未初始化');
              alert('下载功能未初始化，请刷新页面重试');
            }
          });
        });

        // 添加页面过渡效果
        document.querySelectorAll('a:not(.download-btn)').forEach(link => {
          if (
            link.getAttribute('href') &&
            !link.getAttribute('href').startsWith('#') &&
            !link.getAttribute('href').startsWith('javascript:')
          ) {
            link.addEventListener('click', function (e) {
              const href = this.getAttribute('href');
              if (href) {
                e.preventDefault();
                document.body.style.opacity = 0;
                setTimeout(() => {
                  window.location.href = href;
                }, 500);
              }
            });
          }
        });

        // 图片加载优化
        const imgElements = document.querySelectorAll('img');
        let loadedCount = 0;
        const totalImages = imgElements.length;

        // 图片加载完成处理
        function handleImageLoaded() {
          loadedCount++;
          if (loadedCount === totalImages) {
            console.log('所有图片加载完成');
          }
        }

        // 监听图片加载
        imgElements.forEach(img => {
          if (img.complete) {
            handleImageLoaded();
          } else {
            img.addEventListener('load', handleImageLoaded);
            img.addEventListener('error', function () {
              console.error('图片加载失败:', img.src);
              handleImageLoaded();
            });
          }
        });
      });
    </script>
  </body>
</html>
