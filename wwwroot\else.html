<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Else - 游星的档案馆</title>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="audio/background2-「酣梦于彼岸深红」.mp3" as="audio" type="audio/mpeg" />
    <link
      rel="preload"
      href="https://source.unsplash.com/random/1920x1080/?fantasy,abstract"
      as="image"
      fetchpriority="high"
    />
    <link rel="preload" href="audio/audio-controller.js" as="script" />
    <link rel="preload" href="audio/page-loader.js" as="script" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <script src="audio/page-loader.js"></script>
    <script src="audio/audio-controller.js"></script>

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500&family=Cormorant+Garamond:wght@300;400&family=Playfair+Display:ital@0;1&display=swap');

      body {
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
        background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
        color: #fff;
        margin: 0;
        padding: 0;
        min-height: 100vh;
        position: relative;
      }

      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('https://source.unsplash.com/random/1920x1080/?fantasy,abstract');
        background-size: cover;
        background-position: center;
        opacity: 0.2;
        z-index: -1;
      }

      .header {
        text-align: center;
        padding: 2rem 0;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      }

      .header h1 {
        font-size: 3rem;
        margin: 0;
        background: linear-gradient(to right, #c471ed, #f64f59);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        font-weight: 300;
        letter-spacing: 2px;
        font-family: 'Cinzel', serif;
      }

      .back-btn {
        position: absolute;
        top: 20px;
        left: 20px;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #fff;
        border-radius: 30px;
        text-decoration: none;
        font-size: 1rem;
        transition: all 0.3s ease;
      }

      .back-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
      }

      .characters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
      }

      .character-card {
        background: rgba(0, 0, 0, 0.5);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .character-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
        border-color: rgba(196, 113, 237, 0.5);
      }

      .character-image {
        width: 100%;
        height: 350px;
        object-fit: cover;
        display: block;
        background-color: #2a4365;
        position: relative;
      }

      .character-image-placeholder {
        width: 100%;
        height: 350px;
        background: linear-gradient(45deg, #1a1a2e, #4a5568);
        display: flex;
        justify-content: center;
        align-items: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 1rem;
      }

      .character-info {
        padding: 1.5rem;
      }

      .character-name {
        margin: 0 0 0.5rem 0;
        font-size: 1.5rem;
        color: #c471ed;
        font-weight: 500;
        letter-spacing: 0.5px;
      }

      .character-title {
        margin: 0;
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.7);
      }

      .character-detail {
        background: rgba(0, 0, 0, 0.7);
        padding: 2rem;
        border-radius: 15px;
        margin-top: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        display: none;
        animation: fadeIn 0.5s ease-in-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .character-detail.active {
        display: block;
      }

      .detail-header {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        border-bottom: 1px solid rgba(196, 113, 237, 0.2);
        padding-bottom: 1.5rem;
        position: relative;
      }

      .detail-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100px;
        height: 2px;
        background: linear-gradient(to right, #c471ed, transparent);
      }

      .detail-image {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #c471ed;
        box-shadow: 0 0 15px rgba(196, 113, 237, 0.5);
      }

      .detail-image-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: linear-gradient(45deg, #1a1a2e, #4a5568);
        display: flex;
        justify-content: center;
        align-items: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.8rem;
        text-align: center;
        border: 3px solid #c471ed;
      }

      .detail-title {
        margin-left: 2rem;
      }

      .detail-title h2 {
        margin: 0 0 0.5rem 0;
        font-size: 2.5rem;
        color: #c471ed;
        font-weight: 400;
        letter-spacing: 1px;
      }

      .detail-title p {
        margin: 0;
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.7);
      }

      .detail-content {
        line-height: 1.8;
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
      }

      .detail-content h3 {
        color: #c471ed;
        margin: 1.5rem 0 1rem;
        border-bottom: none;
        padding-bottom: 0.5rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        display: inline-block;
        position: relative;
      }

      .detail-content h3::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(to right, #c471ed, transparent);
      }

      .detail-content p {
        margin-bottom: 1rem;
      }

      .detail-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .detail-close:hover {
        color: #fff;
        transform: rotate(90deg);
      }

      .upload-section {
        margin-top: 2rem;
        background: rgba(0, 0, 0, 0.5);
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        backdrop-filter: blur(5px);
      }

      .upload-section h2 {
        color: #c471ed;
        margin-top: 0;
        font-family: 'Cinzel', serif;
        letter-spacing: 1px;
      }

      .upload-info {
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.7);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .characters-grid {
          grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        }

        .detail-header {
          flex-direction: column;
          text-align: center;
        }

        .detail-title {
          margin-left: 0;
          margin-top: 1rem;
        }

        .character-image,
        .character-image-placeholder {
          height: 250px;
        }
      }
    </style>
  </head>
  <body>
    <a href="index.html" class="back-btn">← 返回主页</a>

    <header class="header">
      <h1>Else</h1>
    </header>

    <div class="container">
      <div class="characters-grid">
        <!-- 角色卡1 -->
        <div class="character-card" data-character="character1">
          <div class="character-image-placeholder">点击上传角色图片</div>
          <div class="character-info">
            <h3 class="character-name">角色名称1</h3>
            <p class="character-title">角色称号/身份</p>
          </div>
        </div>

        <!-- 角色卡2 -->
        <div class="character-card" data-character="character2">
          <div class="character-image-placeholder">点击上传角色图片</div>
          <div class="character-info">
            <h3 class="character-name">角色名称2</h3>
            <p class="character-title">角色称号/身份</p>
          </div>
        </div>

        <!-- 角色卡3 -->
        <div class="character-card" data-character="character3">
          <div class="character-image-placeholder">点击上传角色图片</div>
          <div class="character-info">
            <h3 class="character-name">角色名称3</h3>
            <p class="character-title">角色称号/身份</p>
          </div>
        </div>

        <!-- 角色卡4 -->
        <div class="character-card" data-character="character4">
          <div class="character-image-placeholder">点击上传角色图片</div>
          <div class="character-info">
            <h3 class="character-name">角色名称4</h3>
            <p class="character-title">角色称号/身份</p>
          </div>
        </div>

        <!-- 角色卡5 -->
        <div class="character-card" data-character="character5">
          <div class="character-image-placeholder">点击上传角色图片</div>
          <div class="character-info">
            <h3 class="character-name">角色名称5</h3>
            <p class="character-title">角色称号/身份</p>
          </div>
        </div>

        <!-- 角色卡6 -->
        <div class="character-card" data-character="character6">
          <div class="character-image-placeholder">点击上传角色图片</div>
          <div class="character-info">
            <h3 class="character-name">角色名称6</h3>
            <p class="character-title">角色称号/身份</p>
          </div>
        </div>
      </div>

      <!-- 角色详情区域 -->
      <div id="character1" class="character-detail">
        <button class="detail-close">&times;</button>
        <div class="detail-header">
          <div class="detail-image-placeholder">点击上传高清角色图片</div>
          <div class="detail-title">
            <h2>角色名称1</h2>
            <p>角色称号/身份</p>
          </div>
        </div>
        <div class="detail-content">
          <h3>角色背景</h3>
          <p contenteditable="true">在这里编辑角色的背景故事...</p>

          <h3>性格特点</h3>
          <p contenteditable="true">在这里编辑角色的性格特点...</p>

          <h3>能力介绍</h3>
          <p contenteditable="true">在这里编辑角色的能力介绍...</p>

          <h3>关系网络</h3>
          <p contenteditable="true">在这里编辑角色的人际关系...</p>
        </div>
      </div>

      <div id="character2" class="character-detail">
        <button class="detail-close">&times;</button>
        <div class="detail-header">
          <div class="detail-image-placeholder">点击上传高清角色图片</div>
          <div class="detail-title">
            <h2>角色名称2</h2>
            <p>角色称号/身份</p>
          </div>
        </div>
        <div class="detail-content">
          <h3>角色背景</h3>
          <p contenteditable="true">在这里编辑角色的背景故事...</p>

          <h3>性格特点</h3>
          <p contenteditable="true">在这里编辑角色的性格特点...</p>

          <h3>能力介绍</h3>
          <p contenteditable="true">在这里编辑角色的能力介绍...</p>

          <h3>关系网络</h3>
          <p contenteditable="true">在这里编辑角色的人际关系...</p>
        </div>
      </div>

      <!-- 其他角色详情区域（3-6） -->
      <div id="character3" class="character-detail">
        <button class="detail-close">&times;</button>
        <div class="detail-header">
          <div class="detail-image-placeholder">点击上传高清角色图片</div>
          <div class="detail-title">
            <h2>角色名称3</h2>
            <p>角色称号/身份</p>
          </div>
        </div>
        <div class="detail-content">
          <h3>角色背景</h3>
          <p contenteditable="true">在这里编辑角色的背景故事...</p>

          <h3>性格特点</h3>
          <p contenteditable="true">在这里编辑角色的性格特点...</p>

          <h3>能力介绍</h3>
          <p contenteditable="true">在这里编辑角色的能力介绍...</p>

          <h3>关系网络</h3>
          <p contenteditable="true">在这里编辑角色的人际关系...</p>
        </div>
      </div>

      <div id="character4" class="character-detail">
        <button class="detail-close">&times;</button>
        <div class="detail-header">
          <div class="detail-image-placeholder">点击上传高清角色图片</div>
          <div class="detail-title">
            <h2>角色名称4</h2>
            <p>角色称号/身份</p>
          </div>
        </div>
        <div class="detail-content">
          <h3>角色背景</h3>
          <p contenteditable="true">在这里编辑角色的背景故事...</p>

          <h3>性格特点</h3>
          <p contenteditable="true">在这里编辑角色的性格特点...</p>

          <h3>能力介绍</h3>
          <p contenteditable="true">在这里编辑角色的能力介绍...</p>

          <h3>关系网络</h3>
          <p contenteditable="true">在这里编辑角色的人际关系...</p>
        </div>
      </div>

      <div id="character5" class="character-detail">
        <button class="detail-close">&times;</button>
        <div class="detail-header">
          <div class="detail-image-placeholder">点击上传高清角色图片</div>
          <div class="detail-title">
            <h2>角色名称5</h2>
            <p>角色称号/身份</p>
          </div>
        </div>
        <div class="detail-content">
          <h3>角色背景</h3>
          <p contenteditable="true">在这里编辑角色的背景故事...</p>

          <h3>性格特点</h3>
          <p contenteditable="true">在这里编辑角色的性格特点...</p>

          <h3>能力介绍</h3>
          <p contenteditable="true">在这里编辑角色的能力介绍...</p>

          <h3>关系网络</h3>
          <p contenteditable="true">在这里编辑角色的人际关系...</p>
        </div>
      </div>

      <div id="character6" class="character-detail">
        <button class="detail-close">&times;</button>
        <div class="detail-header">
          <div class="detail-image-placeholder">点击上传高清角色图片</div>
          <div class="detail-title">
            <h2>角色名称6</h2>
            <p>角色称号/身份</p>
          </div>
        </div>
        <div class="detail-content">
          <h3>角色背景</h3>
          <p contenteditable="true">在这里编辑角色的背景故事...</p>

          <h3>性格特点</h3>
          <p contenteditable="true">在这里编辑角色的性格特点...</p>

          <h3>能力介绍</h3>
          <p contenteditable="true">在这里编辑角色的能力介绍...</p>

          <h3>关系网络</h3>
          <p contenteditable="true">在这里编辑角色的人际关系...</p>
        </div>
      </div>

      <div class="upload-section">
        <h2>添加新角色</h2>
        <p class="upload-info">您可以点击角色卡片上传图片并编辑角色信息</p>
        <p class="upload-info">所有角色信息支持直接编辑，点击文本即可修改</p>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // 角色卡点击事件
        const characterCards = document.querySelectorAll('.character-card');
        const characterDetails = document.querySelectorAll('.character-detail');
        const closeButtons = document.querySelectorAll('.detail-close');

        characterCards.forEach(card => {
          card.addEventListener('click', function () {
            const characterId = this.getAttribute('data-character');
            const detail = document.getElementById(characterId);

            // 隐藏所有角色详情
            characterDetails.forEach(detail => {
              detail.classList.remove('active');
            });

            // 显示当前角色详情
            if (detail) {
              detail.classList.add('active');
              // 滚动到详情区域
              detail.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          });
        });

        // 关闭按钮点击事件
        closeButtons.forEach(button => {
          button.addEventListener('click', function (e) {
            e.stopPropagation();
            const detail = this.closest('.character-detail');
            detail.classList.remove('active');
          });
        });

        // 图片上传功能（模拟）
        const imagePlaceholders = document.querySelectorAll('.character-image-placeholder, .detail-image-placeholder');

        imagePlaceholders.forEach(placeholder => {
          placeholder.addEventListener('click', function (e) {
            e.stopPropagation();
            alert('此功能已禁用');
          });
        });

        // 使角色名称和称号可编辑
        const characterNames = document.querySelectorAll('.character-name');
        const characterTitles = document.querySelectorAll('.character-title');
        const detailNames = document.querySelectorAll('.detail-title h2');
        const detailTitles = document.querySelectorAll('.detail-title p');

        function makeEditable(elements) {
          elements.forEach(element => {
            element.setAttribute('contenteditable', 'true');
            element.addEventListener('focus', function () {
              const range = document.createRange();
              range.selectNodeContents(this);
              const selection = window.getSelection();
              selection.removeAllRanges();
              selection.addRange(range);
            });
          });
        }

        makeEditable(characterNames);
        makeEditable(characterTitles);
        makeEditable(detailNames);
        makeEditable(detailTitles);

        // 同步角色卡和详情页的名称和称号
        characterNames.forEach((name, index) => {
          name.addEventListener('input', function () {
            if (detailNames[index]) {
              detailNames[index].textContent = this.textContent;
            }
          });
        });

        characterTitles.forEach((title, index) => {
          title.addEventListener('input', function () {
            if (detailTitles[index]) {
              detailTitles[index].textContent = this.textContent;
            }
          });
        });
      });
    </script>
  </body>
</html>
