<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游星的档案馆</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="audio/background.mp3" as="audio" type="audio/mpeg">
    <link rel="preload" href="https://source.unsplash.com/random/1920x1080/?stars,space,galaxy" as="image" fetchpriority="high">
    <link rel="preload" href="audio/audio-controller.js" as="script">
    <link rel="preload" href="audio/page-loader.js" as="script">
    <link rel="preload" href="audio/download-handler.js" as="script">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500&family=Cormorant+Garamond:wght@300;400&family=Playfair+Display:ital@0;1&display=swap" as="style">
    
    <!-- 优先加载脚本 -->
    <script src="audio/download-handler.js"></script>
    <script src="audio/page-loader.js"></script>
    <script src="audio/audio-controller.js"></script>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500&family=Cormorant+Garamond:wght@300;400&family=Playfair+Display:ital@0;1&display=swap');
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #fff;
            height: 100vh;
            margin: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://source.unsplash.com/random/1920x1080/?stars,space,galaxy');
            background-size: cover;
            background-position: center;
            opacity: 0.4;
            z-index: -1;
            animation: slowZoom 30s infinite alternate;
        }
        
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, transparent 30%, rgba(0, 0, 0, 0.6) 100%);
            pointer-events: none;
            z-index: -1;
        }
        
        @keyframes slowZoom {
            0% {
                transform: scale(1);
            }
            100% {
                transform: scale(1.1);
            }
        }
        
        .container {
            text-align: center;
            padding: 3.5rem;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 20px;
            box-shadow: 0 0 40px rgba(0, 255, 255, 0.3), inset 0 0 20px rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            max-width: 800px;
            width: 90%;
            position: relative;
            overflow: hidden;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(transparent, rgba(72, 202, 228, 0.1), transparent 30%);
            animation: rotate 10s linear infinite;
            z-index: -1;
        }
        
        @keyframes rotate {
            100% {
                transform: rotate(360deg);
            }
        }
        
        .title-container {
            position: relative;
            margin-bottom: 4rem;
            animation: floatUpDown 6s ease-in-out infinite;
        }
        
        @keyframes floatUpDown {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .title {
            font-size: 4.8rem;
            margin-bottom: 0;
            font-weight: 300;
            background: linear-gradient(to right, #ff6b6b, #ffe66d, #4ecdc4, #a5dee5);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
            animation: glow 3s ease-in-out infinite alternate;
            letter-spacing: 5px;
            position: relative;
        }
        
        .title-decoration {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 180px;
            height: 2px;
            background: linear-gradient(to right, transparent, #a5dee5, transparent);
            box-shadow: 0 0 10px rgba(165, 222, 229, 0.5);
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 1rem;
            letter-spacing: 2px;
            font-weight: 300;
            opacity: 0;
            animation: fadeIn 1s ease-out 0.5s forwards;
            position: relative;
            padding: 0 10px;
            display: inline-block;
        }
        
        .subtitle::before, .subtitle::after {
            content: '✦';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(165, 222, 229, 0.6);
            font-size: 0.8rem;
        }
        
        .subtitle::before {
            left: -15px;
            animation: pulseLeft 3s infinite ease-in-out;
        }
        
        .subtitle::after {
            right: -15px;
            animation: pulseRight 3s infinite ease-in-out;
        }
        
        @keyframes pulseLeft {
            0%, 100% { transform: translateY(-50%) scale(1); opacity: 0.6; }
            50% { transform: translateY(-50%) scale(1.3); opacity: 1; }
        }
        
        @keyframes pulseRight {
            0%, 100% { transform: translateY(-50%) scale(1); opacity: 0.6; }
            50% { transform: translateY(-50%) scale(1.3); opacity: 1; }
        }
        
        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes glow {
            0% {
                text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
            }
            100% {
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.7), 0 0 30px rgba(255, 255, 255, 0.5);
            }
        }
        
        .buttons {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2.5rem;
            perspective: 1000px;
            flex-wrap: wrap;
            max-width: 90%;
        }
        
        .btn {
            padding: 1.1rem 2.5rem;
            font-size: 1.3rem;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(5px);
            position: relative;
            overflow: hidden;
            z-index: 1;
            letter-spacing: 3px;
            font-weight: 400;
            text-decoration: none;
            transform-style: preserve-3d;
            transform: translateZ(0);
            opacity: 0;
            animation: buttonAppear 0.6s ease-out forwards;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), inset 0 1px rgba(255, 255, 255, 0.1);
            font-family: 'Cinzel', serif;
            text-transform: uppercase;
            margin: 0.5rem;
            min-width: 200px;
            text-align: center;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, transparent 0%, transparent 25%, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.1) 50%, transparent 50%, transparent 75%, rgba(255, 255, 255, 0.1) 75%, rgba(255, 255, 255, 0.1) 100%);
            background-size: 250% 250%;
            animation: shimmer 8s linear infinite;
            z-index: -1;
        }
        
        @keyframes shimmer {
            0% {
                background-position: 0% 0%;
            }
            100% {
                background-position: 100% 100%;
            }
        }
        
        .btn span {
            display: block;
            font-family: 'Cormorant Garamond', serif;
            font-style: italic;
            font-size: 0.85rem;
            margin-top: 5px;
            letter-spacing: 1px;
            opacity: 0.8;
            transform: translateY(0);
            transition: all 0.3s ease;
            font-weight: 300;
            text-transform: none;
        }
        
        .btn:hover span {
            opacity: 1;
            transform: translateY(2px);
        }
        
        .btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 25px rgba(0, 0, 0, 0.4), 0 0 20px rgba(72, 202, 228, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            letter-spacing: 3.5px;
        }
        
        .btn:nth-child(1) {
            animation-delay: 0.8s;
        }
        
        .btn:nth-child(2) {
            animation-delay: 1.2s;
        }
        
        .btn:nth-child(3) {
            animation-delay: 1.6s;
        }
        
        @keyframes buttonAppear {
            0% {
                opacity: 0;
                transform: translateY(20px) rotateX(30deg);
            }
            100% {
                opacity: 1;
                transform: translateY(0) rotateX(0);
            }
        }
        
        .btn:active {
            transform: translateY(0) scale(0.98);
        }
        
        .stars {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }
        
        .star {
            position: absolute;
            background-color: #fff;
            border-radius: 50%;
            animation: twinkle 2s infinite ease-in-out;
        }
        
        .shooting-star {
            position: absolute;
            width: 100px;
            height: 1px;
            background: linear-gradient(to right, rgba(255, 255, 255, 0), #fff 50%, rgba(255, 255, 255, 0));
            transform: rotate(-45deg);
            opacity: 0;
            animation: shootingStar 6s linear infinite;
        }
        
        @keyframes shootingStar {
            0% {
                transform: translateX(-100px) translateY(-100px) rotate(-45deg);
                opacity: 0;
            }
            10%, 15% {
                opacity: 1;
            }
            20% {
                transform: translateX(calc(100vw + 100px)) translateY(calc(100vh + 100px)) rotate(-45deg);
                opacity: 0;
            }
            100% {
                opacity: 0;
            }
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            overflow: hidden;
            z-index: -1;
        }
        
        .particle {
            position: absolute;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
            border-radius: 50%;
            opacity: 0.3;
            animation: float 15s infinite linear;
            filter: blur(1px);
        }
        
        .audio-tip {
            position: fixed;
            bottom: 70px;
            right: 20px;
            background: rgba(0, 0, 0, 0.6);
            color: #fff;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            opacity: 0;
            transform: translateY(20px);
            animation: tipFadeIn 1s forwards 2s, tipFadeOut 1s forwards 8s;
            pointer-events: none;
            z-index: 100;
        }
        
        @keyframes tipFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes tipFadeOut {
            to {
                opacity: 0;
                transform: translateY(-20px);
            }
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="title-container">
            <h1 class="title">游星的档案馆</h1>
            <div class="title-decoration"></div>
            <p class="subtitle">即使路途坎坷颠簸，我也有需要坚持的事</p>
        </div>
        <div class="buttons">
            <a href="mingchao.html" class="btn">Wuthering Waves<span>Echo of the Elements</span></a>
            <a href="https://hcn91ne4xcsa.feishu.cn/wiki/QpnLwAtOai4Gv1kj3oFctIm1n5e?from=from_copylink" class="btn" target="_blank">Summer Pockets<span>Reflection Blue</span></a>
            <a href="else.html" class="btn">Else<span>Other Characters</span></a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 确保音频控制器正常工作
            if (window.audioController && window.audioController.audio) {
                // 调整音频控制器的z-index，确保它显示在最上层
                const audioControl = document.getElementById('audioControl');
                if (audioControl) {
                    audioControl.style.zIndex = "10000";
                }
                
                // 强制在页面加载时恢复音频播放状态
                const audioState = localStorage.getItem('audioState') || 'on';
                if (audioState === 'on' && window.audioController.audio.paused) {
                    window.audioController.audio.play().catch(e => {
                        console.log('自动播放被阻止，等待用户交互', e);
                    });
                }
            }
            
            // 创建星星背景
            createStars();
            createParticles();
        });
        
        // 创建星星背景
        function createStars() {
            const stars = document.getElementById('stars');
            const count = 150;
            
            stars.innerHTML = '';
            
            for (let i = 0; i < count; i++) {
                const star = document.createElement('div');
                star.classList.add('star');
                
                // 随机位置
                const x = Math.floor(Math.random() * window.innerWidth);
                const y = Math.floor(Math.random() * window.innerHeight);
                
                // 随机大小
                const size = Math.random() * 3;
                
                // 随机动画延迟
                const delay = Math.random() * 2;
                
                star.style.left = `${x}px`;
                star.style.top = `${y}px`;
                star.style.width = `${size}px`;
                star.style.height = `${size}px`;
                star.style.animationDelay = `${delay}s`;
                
                stars.appendChild(star);
            }
            
            // 添加流星
            for (let i = 0; i < 5; i++) {
                const shootingStar = document.createElement('div');
                shootingStar.classList.add('shooting-star');
                
                const x = Math.floor(Math.random() * window.innerWidth);
                const y = Math.floor(Math.random() * window.innerHeight / 2);
                const delay = Math.random() * 15;
                const duration = 5 + Math.random() * 10;
                
                shootingStar.style.left = `${x}px`;
                shootingStar.style.top = `${y}px`;
                shootingStar.style.animationDelay = `${delay}s`;
                shootingStar.style.animationDuration = `${duration}s`;
                
                stars.appendChild(shootingStar);
            }
        }
        
        // 创建浮动粒子
        function createParticles() {
            const particles = document.getElementById('particles');
            const count = 20;
            
            particles.innerHTML = '';
            
            for (let i = 0; i < count; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');
                
                const size = 5 + Math.random() * 20;
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                const duration = 15 + Math.random() * 30;
                const delay = Math.random() * 5;
                
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${x}vw`;
                particle.style.top = `${y}vh`;
                particle.style.animation = `float ${duration}s infinite linear`;
                particle.style.animationDelay = `-${delay}s`;
                
                particles.appendChild(particle);
            }
        }
        
        // 浮动动画
        document.styleSheets[0].insertRule(`
            @keyframes float {
                0% {
                    transform: translate(0, 0);
                }
                25% {
                    transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
                }
                50% {
                    transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
                }
                75% {
                    transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
                }
                100% {
                    transform: translate(0, 0);
                }
            }
        `, document.styleSheets[0].cssRules.length);
        
        window.addEventListener('resize', () => {
            createStars();
        });
    </script>
</body>
</html> 