/**
 * 简化版音频控制器 - 实现全站背景音乐播放
 */
class AudioController {
  constructor() {
    this.createAudio();
    this.createControl();
    this.setupEvents();
  }

  createAudio() {
    this.audio = new Audio('audio/background2-「酣梦于彼岸深红」.mp3');
    this.audio.loop = true;
    this.audio.volume = 0.3;
    this.audioState = localStorage.getItem('audioState') || 'on';

    if (this.audioState === 'on') {
      this.audio.play().catch(e => console.log('等待用户交互后播放'));
    }
  }

  createControl() {
    const control = document.createElement('div');
    control.id = 'audioControl';
    control.style.cssText =
      'position:fixed;top:20px;right:20px;width:40px;height:40px;background:rgba(0,0,0,0.6);border-radius:50%;display:flex;justify-content:center;align-items:center;cursor:pointer;z-index:10000;';

    const icon = document.createElement('span');
    icon.id = 'audioIcon';
    icon.style.color = 'white';
    icon.innerHTML = this.audioState === 'on' ? '♪' : '♪̸';

    control.appendChild(icon);
    document.body.appendChild(control);
  }

  setupEvents() {
    const control = document.getElementById('audioControl');
    if (control) {
      control.addEventListener('click', () => this.toggleAudio());
    }

    document.addEventListener(
      'click',
      () => {
        if (this.audioState === 'on' && this.audio.paused) {
          this.audio.play();
        }
      },
      { once: true },
    );

    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.audioState === 'on' && this.audio.paused) {
        this.audio.play().catch(e => {});
      }
    });
  }

  toggleAudio() {
    const newState = this.audioState === 'on' ? 'off' : 'on';
    this.audioState = newState;
    localStorage.setItem('audioState', newState);

    const icon = document.getElementById('audioIcon');
    if (icon) {
      icon.innerHTML = newState === 'on' ? '♪' : '♪̸';
    }

    if (newState === 'on') {
      this.audio.play().catch(e => {});
    } else {
      this.audio.pause();
    }
  }
}

// 初始化全局音频控制器
window.audioController = new AudioController();
